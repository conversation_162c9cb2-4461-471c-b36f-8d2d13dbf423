import React, { useState } from "react";
import { Flag, Image as ImageIcon } from "lucide-react";
import { VscFilePdf } from "react-icons/vsc";
import { FaFileWord } from "react-icons/fa";
import { HiUserCircle } from "react-icons/hi";
import { FaUserGroup } from "react-icons/fa6";
import { IoIosNotificationsOutline } from "react-icons/io";
import { useUserCount } from "../hooks/useUserCount";

/**
 * NoticePreview Component
 * Real-time preview of the notice as it will appear when posted
 */
const NoticePreview = ({ data, currentUser, isInModal = false }) => {
  const [expandedTitle, setExpandedTitle] = useState(false);

  // Use the custom hook for user count calculation
  // Handle both selectedUnits (from create/edit form) and target_units_data (from history/existing notices)
  const targetUnits =
    data.selectedUnits || data.target_units_data?.map((unit) => unit.id) || [];
  const { userCount, loading: loadingUserCount } = useUserCount(targetUnits);

  // Get priority configuration
  const getPriorityConfig = (priority) => {
    const configs = {
      urgent: { color: "#EF4444", bgColor: "#FEF2F2", label: "Urgent" },
      high: { color: "#F59E0B", bgColor: "#FFFBEB", label: "High" },
      normal: { color: "#3D9D9B", bgColor: "#F0FDF4", label: "Normal" },
      low: { color: "#6B7280", bgColor: "#F9FAFB", label: "Low" }
    };
    return configs[priority?.toLowerCase()] || null;
  };

  // Get notice status
  const getStatus = () => {
    if (!data.startDate || !data.startTime || !data.endDate || !data.endTime) {
      return { status: "Draft", color: "#6B7280", bgColor: "#F9FAFB" };
    }

    try {
      const now = new Date();

      // Handle different date formats and ensure proper parsing
      let startDateStr = data.startDate;
      let endDateStr = data.endDate;

      // If dates are in YYYY-MM-DD format, use them directly
      if (typeof data.startDate === "string" && data.startDate.includes("-")) {
        startDateStr = data.startDate;
      } else {
        // Convert to YYYY-MM-DD format if needed
        const startDateObj = new Date(data.startDate);
        startDateStr = startDateObj.toISOString().split("T")[0];
      }

      if (typeof data.endDate === "string" && data.endDate.includes("-")) {
        endDateStr = data.endDate;
      } else {
        // Convert to YYYY-MM-DD format if needed
        const endDateObj = new Date(data.endDate);
        endDateStr = endDateObj.toISOString().split("T")[0];
      }

      // Create datetime objects for comparison
      const startDateTime = new Date(`${startDateStr}T${data.startTime}`);
      const endDateTime = new Date(`${endDateStr}T${data.endTime}`);

      if (now < startDateTime) {
        return { status: "Upcoming", color: "#F59E0B", bgColor: "#FFFBEB" };
      } else if (now >= startDateTime && now <= endDateTime) {
        return { status: "On Going", color: "#10B981", bgColor: "#F0FDF4" };
      } else {
        return { status: "Expired", color: "#EF4444", bgColor: "#FEF2F2" };
      }
    } catch (error) {
      console.warn("Error calculating status in preview:", error);
      return { status: "Draft", color: "#6B7280", bgColor: "#F9FAFB" };
    }
  };

  // Format date and time
  const formatDateTime = (date, time) => {
    if (!date || !time) return "";

    let formattedDate = "";

    // Handle different date formats
    if (typeof date === "string") {
      // If date is in YYYY-MM-DD format (from Calendar component)
      if (date.includes("-") && date.length === 10) {
        const [year, month, day] = date.split("-");
        formattedDate = `${day}-${month}-${year}`;
      } else {
        // If date is already formatted or other string format
        const dateObj = new Date(date);
        const day = dateObj.getDate().toString().padStart(2, "0");
        const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
        const year = dateObj.getFullYear();
        formattedDate = `${day}-${month}-${year}`;
      }
    } else {
      // If date is a Date object
      const dateObj = new Date(date);
      const day = dateObj.getDate().toString().padStart(2, "0");
      const month = (dateObj.getMonth() + 1).toString().padStart(2, "0");
      const year = dateObj.getFullYear();
      formattedDate = `${day}-${month}-${year}`;
    }

    // Format time
    let formattedTime = "";
    if (time) {
      // Handle time in HH:MM format
      const [hours, minutes] = time.split(":");
      const hour24 = parseInt(hours, 10);
      const hour12 = hour24 === 0 ? 12 : hour24 > 12 ? hour24 - 12 : hour24;
      const ampm = hour24 >= 12 ? "pm" : "am";
      formattedTime = `${hour12}:${minutes}${ampm}`;
    }

    return formattedTime ? `${formattedDate} at ${formattedTime}` : formattedDate;
  };

  // Handle title expansion
  const handleTitleHover = () => {
    if (data.title && data.title.length > 25) {
      setExpandedTitle(true);
    }
  };

  const handleTitleLeave = () => {
    setExpandedTitle(false);
  };

  const handleTitleClick = () => {
    setExpandedTitle(!expandedTitle);
  };

  // Helper functions for file type detection
  const isPDF = (fileName, fileType) => {
    return fileType === "application/pdf" || fileName?.toLowerCase().endsWith(".pdf");
  };

  const isDoc = (fileName, fileType) => {
    return (
      fileType === "application/msword" ||
      fileType === "application/vnd.openxmlformats-officedocument.wordprocessingml.document" ||
      fileName?.toLowerCase().endsWith(".doc") ||
      fileName?.toLowerCase().endsWith(".docx")
    );
  };

  // Get author information
  const getAuthorInfo = () => {
    switch (data.postAs) {
      case "Creator":
      case "creator":
        return {
          name: data.authorName || currentUser?.full_name || currentUser?.fullName || "Current User",
          displayName: data.authorName || currentUser?.full_name || currentUser?.fullName || "Current User"
        };
      case "Group":
      case "group":
        return {
          name: data.selectedGroupName || "Selected Group",
          displayName: data.selectedGroupName || "Selected Group"
        };
      case "Member":
      case "member":
        return {
          name: data.selectedMemberName || "Selected Member",
          displayName: data.selectedMemberName || "Selected Member"
        };
      default:
        return {
          name: "Unknown",
          displayName: "Unknown"
        };
    }
  };

  // Get current status and priority configurations
  const statusConfig = getStatus();
  const priorityConfig = getPriorityConfig(data.priority);
  const authorInfo = getAuthorInfo();

  // If no data, show empty state
  if (!data || Object.keys(data).length === 0) {
    return (
      <div className="flex flex-col items-center justify-center h-full text-center p-8">
        <IoIosNotificationsOutline className="w-16 h-16 text-gray-300 mb-4" />
        <h3 className="text-lg font-medium text-gray-900 mb-2">Preview</h3>
        <p className="text-gray-500 mb-4">
          This is how your notice will appear to residents
        </p>
        <div className="text-sm text-gray-400">
          Your notice will appear here as you type
        </div>
      </div>
    );
  }

  return (
    <div className="h-full overflow-y-auto">
      <div className="p-4">
        {/* Preview Header */}
        <div className="mb-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-2">Preview</h3>
          <p className="text-sm text-gray-600">
            This is how your notice will appear to residents
          </p>
        </div>

        {/* Notice Card Preview */}
        <div className="border-[1px] border-primary rounded-lg overflow-hidden bg-white shadow-sm ">
          {/* Header with Author Info and Status */}
          <div className="p-4 border-b border-gray-100">
            <div className="flex items-start justify-between">
              {/* Left side - Author Info */}
              <div className="flex items-center space-x-3">
                {/* Author Avatar and Info */}
                <div className="w-8 h-8 rounded-full flex items-center justify-center">
                  {data.postAs === "Creator" || data.postAs === "creator" ? (
                    <HiUserCircle className="w-8 h-8" color="gray" />
                  ) : data.postAs === "Group" || data.postAs === "group" ? (
                    <FaUserGroup className="w-8 h-8" color="gray" />
                  ) : data.postAs === "Member" || data.postAs === "member" ? (
                    <HiUserCircle className="w-8 h-8" color="gray" />
                  ) : (
                    <HiUserCircle className="w-8 h-8" color="gray" />
                  )}
                </div>
                <div>
                  <h4 className="text-sm font-semibold text-gray-900">
                    {authorInfo.displayName}
                  </h4>
                  <p className="text-xs text-gray-500">
                    {data.postAs === "Creator" || data.postAs === "creator"
                      ? "Creator"
                      : data.postAs === "Group" || data.postAs === "group"
                      ? "Group"
                      : data.postAs === "Member" || data.postAs === "member"
                      ? "Member"
                      : "Unknown"}
                  </p>
                </div>
              </div>

              {/* Right side - Priority Flag, Status and Notification */}
              <div className="flex items-center space-x-2">
                {/* Priority Flag */}
                {data.priority && priorityConfig && (
                  <Flag
                    className="w-4 h-4"
                    style={{ color: priorityConfig.color }}
                    fill={priorityConfig.color}
                  />
                )}
                <IoIosNotificationsOutline
                  className="w-4 h-4"
                  style={{ color: statusConfig.color }}
                />
                <span className="text-xs font-medium text-gray-700 flex items-center">
                  {loadingUserCount && userCount === 0 ? (
                    <div className="flex items-center">
                      <div className="w-3 h-3 border border-gray-400 border-t-transparent rounded-full animate-spin mr-1"></div>
                      0
                    </div>
                  ) : (
                    userCount
                  )}
                </span>
              </div>
            </div>

            {/* Date Info */}
            <div className="mt-2 text-[10px] flex items-center gap-3">
              <span className="text-primary font-bold whitespace-nowrap">
                Start: {formatDateTime(data.startDate, data.startTime) || ""}
              </span>
              <span className="text-[#FF8682] font-bold whitespace-nowrap">
                Expire: {formatDateTime(data.endDate, data.endTime) || ""}
              </span>
            </div>

            {/* Label Info - Next line */}
            {data.label && (
              <div className="mt-1 text-xs">
                <div className="flex flex-wrap gap-1">
                  {data.label.split(",").map((label, index) => (
                    <span
                      key={index}
                      className="bg-[#F5F5F5] text-[#090909] text-[10px] px-2 py-1 rounded font-bold"
                    >
                      {label.trim()}
                    </span>
                  ))}
                </div>
              </div>
            )}
          </div>

          {/* Content */}
          <div className="p-4">
            {/* Title */}
            <div
              className="mb-3 cursor-pointer"
              onMouseEnter={handleTitleHover}
              onMouseLeave={handleTitleLeave}
              onClick={handleTitleClick}
            >
              <h4
                className="text-sm font-bold text-gray-900 leading-relaxed break-words transition-all duration-200"
                style={{
                  display: "-webkit-box",
                  WebkitLineClamp: expandedTitle ? "unset" : 2,
                  WebkitBoxOrient: "vertical",
                  overflow: expandedTitle ? "visible" : "hidden",
                  textOverflow: expandedTitle ? "unset" : "ellipsis"
                }}
              >
                {data.title || "Title goes here."}
              </h4>
            </div>

            {/* Description */}
            <div className="mb-4">
              <p className="text-gray-500 text-xs leading-relaxed whitespace-pre-wrap break-words font-bold">
                {data.description || "Description goes here."}
              </p>
            </div>

            {/* Attachments */}
            <div className="mb-4 px-2">
              {data.attachments && data.attachments.length > 0 ? (
                <div className="space-y-3">
                  {/* Main/First Image/PDF - Display prominently */}
                  <div
                    className={`relative bg-gray-100 overflow-hidden transition-all duration-200 w-full max-w-[316px] h-[243px] rounded-[8px] ${
                      isInModal
                        ? "border-4 border-primary shadow-xl"
                        : "border-2 border-gray-200"
                    }`}
                  >
                    {isPDF(data.attachments[0].name, data.attachments[0].type) ? (
                      <div className="w-full h-full flex items-center justify-center">
                        <VscFilePdf className="w-16 h-16 text-red-600 font-bold" />
                        <div className="ml-3 text-center">
                          <div className="text-sm font-medium text-gray-900">
                            PDF Document
                          </div>
                          <div className="text-xs text-gray-500">
                            {data.attachments[0].name}
                          </div>
                    className="absolute left-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                  >
                    ←
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-black bg-opacity-50 text-white p-2 rounded-full hover:bg-opacity-70 transition-opacity"
                  >
                    →
                  </button>

                  {/* Image Counter */}
                  <div className="absolute bottom-2 right-2 bg-black bg-opacity-50 text-white px-2 py-1 rounded text-sm">
                    {currentImageIndex + 1} / {data.attachments.length}
                  </div>
                </>
              )}
            </div>
          )}

          {/* Notice Details */}
          <div className="p-4">
            {/* Label */}
            {data.label && (
              <div className="mb-3">
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                  {data.label}
                </span>
              </div>
            )}

            {/* Date and Time Information */}
            <div className="space-y-2 text-sm text-gray-600">
              <div className="flex items-center">
                <span className="font-medium">Start:</span>
                <span className="ml-2">{formatDateTime(data.startDate, data.startTime)}</span>
              </div>
              <div className="flex items-center">
                <span className="font-medium">End:</span>
                <span className="ml-2">{formatDateTime(data.endDate, data.endTime)}</span>
              </div>
            </div>

            {/* User Count */}
            {targetUnits.length > 0 && (
              <div className="mt-3 pt-3 border-t border-gray-100">
                <div className="flex items-center text-sm text-gray-600">
                  <IoIosNotificationsOutline className="w-4 h-4 mr-2" />
                  <span>
                    {loadingUserCount ? "Calculating..." : `${userCount} residents will be notified`}
                  </span>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default NoticePreview;
